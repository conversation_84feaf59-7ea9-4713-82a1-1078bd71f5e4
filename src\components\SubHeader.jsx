import React from "react";

// Search icon component
const SearchIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path 
      d="M9 17C13.4183 17 17 13.4183 17 9C17 4.58172 13.4183 1 9 1C4.58172 1 1 4.58172 1 9C1 13.4183 4.58172 17 9 17Z" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
    <path 
      d="M19 19L14.65 14.65" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

// Filter icon component
const FilterIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path 
      d="M2 4H14M4 8H12M6 12H10" 
      stroke="currentColor" 
      strokeWidth="1.5" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

// Dropdown arrow icon
const DropdownIcon = () => (
  <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path 
      d="M3 4.5L6 7.5L9 4.5" 
      stroke="currentColor" 
      strokeWidth="1.5" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

// Gmail icon component
const GmailIcon = () => (
  <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path 
      d="M18 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2C20 0.9 19.1 0 18 0ZM18 4L10 9L2 4V2L10 7L18 2V4Z" 
      fill="currentColor"
    />
  </svg>
);

export default function SubHeader() {
  const handleEmailClick = () => {
    // Handle email overlay opening
    console.log("Opening email overlay");
  };

  return (
    <div
      className="fixed flex justify-between items-center bg-red-500 border-b border-gray-200"
      style={{
        width: '1679px',
        height: '41px',
        top: '235px',
        left: '198px',
        zIndex: 45
      }}
    >
      {/* Search Bar */}
      <div className="text-white">SEARCH BAR HERE</div>

      {/* Right Side Controls */}
      <div className="flex items-center gap-3">
        {/* Filters Dropdown */}
        <div 
          className="flex items-center justify-between bg-white border border-gray-300 rounded px-3 py-2 cursor-pointer hover:bg-gray-50"
          style={{
            width: '225px',
            height: '40px',
            gap: '4px'
          }}
        >
          <div className="flex items-center gap-2">
            <FilterIcon />
            <span className="text-gray-700 text-sm font-medium">Filters</span>
          </div>
          <DropdownIcon />
        </div>

        {/* Gmail Icon Button */}
        <button
          onClick={handleEmailClick}
          className="flex items-center justify-center bg-white border border-gray-300 hover:bg-gray-50 transition-colors duration-0"
          style={{
            width: '52px',
            height: '40px',
            borderRadius: '9.76px',
            paddingTop: '6px',
            paddingRight: '12px',
            paddingBottom: '6px',
            paddingLeft: '12px',
            gap: '10px',
            color: 'rgba(51, 59, 131, 1)'
          }}
        >
          <GmailIcon />
        </button>
      </div>
    </div>
  );
}
