import React from "react";

// Import SVG icons
import ArrivalsIcon from "../assets/Arrivals.svg";
import DeparturesIcon from "../assets/Departures.svg";
import StayOverIcon from "../assets/Stay over.svg";
import GroupIcon from "../assets/Group.svg";
import DayUseIcon from "../assets/Day Use.svg";
import CancelledIcon from "../assets/Cancelled.svg";

// Back arrow icon component
const BackArrowIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15 18L9 12L15 6" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Icon component for status cards
const StatusIcon = ({ src, alt }) => (
  <img src={src} alt={alt} className="w-10 h-10" />
);

const statusData = [
  { label: "Arrivals", value: 10, icon: ArrivalsIcon },
  { label: "Departures", value: 9, icon: DeparturesIcon },
  { label: "Stay Overs", value: 10, icon: StayOverIcon },
  { label: "Group", value: 2, icon: GroupIcon },
  { label: "Day Use", value: 2, icon: DayUseIcon },
  { label: "Cancelled", value: 5, icon: CancelledIcon },
  { label: "No Show", value: 2, icon: CancelledIcon }, // Using cancelled icon for No Show as well
];

export default function Header() {
  return (
    <header
      className="fixed top-0 z-50 bg-[rgba(235,235,235,1)] flex flex-col"
      style={{
        width: '1678px',
        height: '215px',
        angle: '0deg',
        opacity: 1,
        left: '199px',
        borderBottomRightRadius: '10px',
        borderBottomLeftRadius: '10px',
        paddingTop: '20px',
        paddingBottom: '20px',
        gap: '20px',
        paddingLeft: '20px',
        paddingRight: '20px'
      }}
    >
      <div className="flex items-center">
        <BackArrowIcon />
        <h1 className="text-2xl font-semibold ml-3 text-black">All Reservations - 40</h1>
      </div>
      <div className="flex gap-5">
        {statusData.map((item) => (
          <div
            key={item.label}
            className="flex items-center border border-gray-300 rounded-lg px-4 py-3 bg-white shadow-sm"
            style={{ minWidth: '200px' }}
          >
            <StatusIcon src={item.icon} alt={item.label} />
            <div className="flex flex-col ml-3">
              <span className="text-gray-600 text-sm font-medium">{item.label}</span>
              <span className="text-blue-900 text-xl font-bold">{item.value}</span>
            </div>
          </div>
        ))}
      </div>
    </header>
  );
}
