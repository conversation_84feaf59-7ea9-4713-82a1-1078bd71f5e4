import React from "react";

// Dummy icon component
const DummyIcon = () => (
  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
    <span className="text-gray-400">*</span>
  </div>
);

const statusData = [
  { label: "Arrivals", value: 10 },
  { label: "Departures", value: 9 },
  { label: "Stay Overs", value: 10 },
  { label: "Group", value: 2 },
  { label: "Day Use", value: 2 },
  { label: "Cancelled", value: 5 },
  { label: "No Show", value: 2 },
];

export default function Header() {
  return (
    <header className="fixed top-0 left-[170px] z-50 bg-[rgba(235,235,235,1)] w-[1750px] h-[215px] px-8 pt-5 pb-5 flex flex-col gap-5 rounded-bl-[10px] rounded-br-[10px] shadow">
      <div className="flex items-center mb-2">
        {/* Dummy back arrow icon */}
        <DummyIcon />
        <h1 className="text-3xl font-semibold ml-2">All Reservations - 40</h1>
      </div>
      <div className="flex gap-4">
        {statusData.map((item) => (
          <div
            key={item.label}
            className="flex items-center border border-gray-300 rounded-lg px-6 py-3 bg-white min-w-[180px] shadow-sm"
          >
            <DummyIcon />
            <div className="flex flex-col ml-2">
              <span className="text-gray-500 text-sm font-medium">{item.label}</span>
              <span className="text-blue-900 text-2xl font-bold">{item.value}</span>
            </div>
          </div>
        ))}
      </div>
    </header>
  );
}
