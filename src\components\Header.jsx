import React from "react";

// Import SVG icons
import ArrivalsIcon from "../assets/Arrivals.svg";
import DeparturesIcon from "../assets/Departures.svg";
import StayOverIcon from "../assets/Stay over.svg";
import GroupIcon from "../assets/Group.svg";
import DayUseIcon from "../assets/Day Use.svg";
import CancelledIcon from "../assets/Cancelled.svg";

// Back arrow icon component
const BackArrowIcon = () => (
  <div className="w-[32.5px] h-[32.19px] opacity-100 pt-[11px] pr-2 pb-[11px] pl-2 gap-[10px] flex items-center justify-center rotate-90">
    <svg width="16.5" height="10.19" viewBox="0 0 16.5 10.19" fill="none" xmlns="http://www.w3.org/2000/svg" className="opacity-100 rotate-90">
      <path d="M15 18L9 12L15 6" stroke="rgba(28, 28, 28, 1)" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  </div>
);

// Icon component for status cards
const StatusIcon = ({ src, alt }) => (
  <img src={src} alt={alt} className="w-10 h-10" />
);

const statusData = [
  { label: "Arrivals", value: 10, icon: ArrivalsIcon },
  { label: "Departures", value: 9, icon: DeparturesIcon },
  { label: "Stay Overs", value: 10, icon: StayOverIcon },
  { label: "Group", value: 2, icon: GroupIcon },
  { label: "Day Use", value: 2, icon: DayUseIcon },
  { label: "Cancelled", value: 5, icon: CancelledIcon },
  { label: "No Show", value: 2, icon: CancelledIcon }, // Using cancelled icon for No Show as well
];

export default function Header() {
  return (
    <header
      className="fixed top-0 left-[199px] z-50 bg-[rgba(235,235,235,1)] w-[1678px] h-[215px] opacity-100 rounded-bl-[10px] rounded-br-[10px] pt-5 pb-5 gap-5 flex flex-col px-5"
    >
      <div className="flex items-center">
        <button className="flex items-center w-[622.19px] h-[91px] rounded-md pt-6 pr-[30px] pb-6 pl-10 gap-5 bg-[rgba(255,255,255,1)] shadow-[4px_4px_10px_0px_rgba(73,44,130,0.1),-2px_-2px_4px_0px_rgba(73,44,130,0.1)]">
          <BackArrowIcon />
          <span className="w-[500px] h-[43px] gap-5 opacity-100 flex items-center text-[32px] font-semibold leading-[100%] tracking-[0%] text-[rgba(31,41,55,1)]" style={{ fontFamily: 'Segoe UI Variable' }}>
            All Reservations - 40
          </span>
        </button>
      </div>
      <div className="flex gap-5">
        {statusData.map((item) => (
          <div
            key={item.label}
            className="flex items-center border border-gray-300 rounded-lg px-4 py-3 bg-white shadow-sm"
            style={{ minWidth: '200px' }}
          >
            <StatusIcon src={item.icon} alt={item.label} />
            <div className="flex flex-col ml-3">
              <span className="text-gray-600 text-sm font-medium">{item.label}</span>
              <span className="text-blue-900 text-xl font-bold">{item.value}</span>
            </div>
          </div>
        ))}
      </div>
    </header>
  );
}
